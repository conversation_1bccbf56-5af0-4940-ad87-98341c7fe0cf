package com.genco.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 奖励统计响应对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "RewardStatisticsResponse对象", description = "奖励统计响应")
public class RewardStatisticsResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "总奖励发放金额")
    private BigDecimal totalRewardAmount;

    @ApiModelProperty(value = "总奖励发放次数")
    private Integer totalRewardCount;

    @ApiModelProperty(value = "今日奖励发放金额")
    private BigDecimal todayRewardAmount;

    @ApiModelProperty(value = "今日奖励发放次数")
    private Integer todayRewardCount;

    @ApiModelProperty(value = "本月奖励发放金额")
    private BigDecimal monthRewardAmount;

    @ApiModelProperty(value = "本月奖励发放次数")
    private Integer monthRewardCount;

    @ApiModelProperty(value = "奖励发放明细列表")
    private List<RewardDetailItem> rewardDetails;

    /**
     * 奖励发放明细项
     */
    @Data
    @ApiModel(value = "RewardDetailItem", description = "奖励发放明细项")
    public static class RewardDetailItem implements Serializable {
        
        @ApiModelProperty(value = "用户ID")
        private Integer uid;
        
        @ApiModelProperty(value = "用户昵称")
        private String nickname;
        
        @ApiModelProperty(value = "奖励金额")
        private BigDecimal rewardAmount;
        
        @ApiModelProperty(value = "奖励类型")
        private String rewardType;
        
        @ApiModelProperty(value = "奖励标题")
        private String rewardTitle;
        
        @ApiModelProperty(value = "发放时间")
        private String createTime;
        
        @ApiModelProperty(value = "备注")
        private String mark;
    }
}
