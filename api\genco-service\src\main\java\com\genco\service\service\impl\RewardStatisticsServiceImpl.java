package com.genco.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.genco.common.model.user.UserBill;
import com.genco.common.request.RewardStatisticsRequest;
import com.genco.common.response.RewardStatisticsResponse;
import com.genco.service.dao.UserBillDao;
import com.genco.service.service.RewardStatisticsService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 奖励统计服务实现类
 */
@Slf4j
@Service
public class RewardStatisticsServiceImpl implements RewardStatisticsService {

    @Autowired
    private UserBillDao userBillDao;

    @Override
    public RewardStatisticsResponse getRewardStatistics(RewardStatisticsRequest request) {
        RewardStatisticsResponse response = new RewardStatisticsResponse();

        // 1. 查询总奖励统计
        LambdaQueryWrapper<UserBill> totalWrapper = buildQueryWrapper(request);
        totalWrapper.eq(UserBill::getPm, 1); // 1 = 获得
        totalWrapper.like(UserBill::getType, "reward"); // 奖励类型
        
        List<UserBill> totalRewards = userBillDao.selectList(totalWrapper);
        BigDecimal totalAmount = totalRewards.stream()
                .map(UserBill::getNumber)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        response.setTotalRewardAmount(totalAmount);
        response.setTotalRewardCount(totalRewards.size());

        // 2. 查询今日奖励统计
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LambdaQueryWrapper<UserBill> todayWrapper = buildQueryWrapper(request);
        todayWrapper.eq(UserBill::getPm, 1);
        todayWrapper.like(UserBill::getType, "reward");
        todayWrapper.apply("DATE(create_time) = {0}", today);
        
        List<UserBill> todayRewards = userBillDao.selectList(todayWrapper);
        BigDecimal todayAmount = todayRewards.stream()
                .map(UserBill::getNumber)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        response.setTodayRewardAmount(todayAmount);
        response.setTodayRewardCount(todayRewards.size());

        // 3. 查询本月奖励统计
        String monthStart = LocalDate.now().withDayOfMonth(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LambdaQueryWrapper<UserBill> monthWrapper = buildQueryWrapper(request);
        monthWrapper.eq(UserBill::getPm, 1);
        monthWrapper.like(UserBill::getType, "reward");
        monthWrapper.apply("DATE(create_time) >= {0}", monthStart);
        
        List<UserBill> monthRewards = userBillDao.selectList(monthWrapper);
        BigDecimal monthAmount = monthRewards.stream()
                .map(UserBill::getNumber)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        response.setMonthRewardAmount(monthAmount);
        response.setMonthRewardCount(monthRewards.size());

        return response;
    }

    @Override
    public PageInfo<RewardStatisticsResponse.RewardDetailItem> getRewardDetailList(RewardStatisticsRequest request) {
        // 设置分页参数
        PageHelper.startPage(request.getPage(), request.getLimit());
        
        // 查询奖励明细
        List<RewardStatisticsResponse.RewardDetailItem> details = userBillDao.selectRewardDetails(request);
        
        return new PageInfo<>(details);
    }

    @Override
    public String exportRewardDetails(RewardStatisticsRequest request) {
        // TODO: 实现导出功能
        // 这里可以使用 EasyExcel 或其他导出工具
        log.info("导出奖励明细功能待实现");
        return "";
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<UserBill> buildQueryWrapper(RewardStatisticsRequest request) {
        LambdaQueryWrapper<UserBill> wrapper = new LambdaQueryWrapper<>();
        
        // 时间范围查询
        if (StringUtils.hasText(request.getStartDate())) {
            wrapper.apply("DATE(create_time) >= {0}", request.getStartDate());
        }
        if (StringUtils.hasText(request.getEndDate())) {
            wrapper.apply("DATE(create_time) <= {0}", request.getEndDate());
        }
        
        // 奖励类型查询
        if (StringUtils.hasText(request.getRewardType())) {
            wrapper.eq(UserBill::getType, request.getRewardType());
        }
        
        // 用户ID查询
        if (request.getUid() != null) {
            wrapper.eq(UserBill::getUid, request.getUid());
        }
        
        // 按创建时间倒序
        wrapper.orderByDesc(UserBill::getCreateTime);
        
        return wrapper;
    }
}
