package com.genco.front.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.genco.common.constants.Constants;
import com.genco.common.constants.SysConfigConstants;
import com.genco.common.constants.SysGroupDataConstants;
import com.genco.common.enums.ProductChannelEnum;
import com.genco.common.exception.CrmebException;
import com.genco.common.model.product.StoreProduct;
import com.genco.common.model.record.UserVisitRecord;
import com.genco.common.model.system.SystemConfig;
import com.genco.common.model.system.SystemUserLevel;
import com.genco.common.model.user.User;
import com.genco.common.page.CommonPage;
import com.genco.common.request.PageParamRequest;
import com.genco.common.response.IndexInfoResponse;
import com.genco.common.response.IndexProductResponse;
import com.genco.common.response.StoreBrandResponse;
import com.genco.common.vo.MyRecord;
import com.genco.front.service.IndexService;
import com.genco.service.delete.ProductUtils;
import com.genco.service.service.*;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * IndexServiceImpl 接口实现
 */
@Service
public class IndexServiceImpl implements IndexService {

    @Autowired
    private SystemGroupDataService systemGroupDataService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private UserService userService;

    @Autowired
    private StoreProductService storeProductService;

    @Autowired
    private ProductUtils productUtils;

    @Autowired
    private UserVisitRecordService userVisitRecordService;

    @Autowired
    private BrandService brandService;

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    /**
     * 首页数据
     * banner、金刚区、广告位
     */
    @Override
    public IndexInfoResponse getIndexInfo() {
        IndexInfoResponse indexInfoResponse = new IndexInfoResponse();
        indexInfoResponse.setBanner(systemGroupDataService.getListMapByGid(Constants.GROUP_DATA_ID_INDEX_BANNER)); //首页banner滚动图
        indexInfoResponse.setMenus(systemGroupDataService.getListMapByGid(Constants.GROUP_DATA_ID_INDEX_MENU)); //导航模块
        indexInfoResponse.setRoll(systemGroupDataService.getListMapByGid(Constants.GROUP_DATA_ID_INDEX_NEWS_BANNER)); //首页滚动新闻

        indexInfoResponse.setLogoUrl(systemConfigService.getValueByKey(Constants.CONFIG_KEY_SITE_LOGO));// 企业logo地址
        indexInfoResponse.setYzfUrl(systemConfigService.getValueByKey(Constants.CONFIG_KEY_YZF_H5_URL));// 云智服H5 url
        indexInfoResponse.setConsumerHotline(systemConfigService.getValueByKey(Constants.CONFIG_KEY_CONSUMER_HOTLINE));// 客服电话
        indexInfoResponse.setTelephoneServiceSwitch(systemConfigService.getValueByKey(Constants.CONFIG_KEY_TELEPHONE_SERVICE_SWITCH));// 客服电话服务
        indexInfoResponse.setCategoryPageConfig(systemConfigService.getValueByKey(Constants.CONFIG_CATEGORY_CONFIG));// 商品分类页配置
        indexInfoResponse.setIsShowCategory(systemConfigService.getValueByKey(Constants.CONFIG_IS_SHOW_CATEGORY));// 是否隐藏一级分类
        indexInfoResponse.setExplosiveMoney(systemGroupDataService.getListMapByGid(Constants.GROUP_DATA_ID_INDEX_EX_BANNER));//首页超值爆款
        indexInfoResponse.setHomePageSaleListStyle(systemConfigService.getValueByKey(Constants.CONFIG_IS_PRODUCT_LIST_STYLE));// 首页商品列表模板配置
        indexInfoResponse.setSubscribe(false);
        User user = userService.getInfo();
        if (ObjectUtil.isNotNull(user) && user.getSubscribe()) {
            indexInfoResponse.setSubscribe(user.getSubscribe());
        }

        // 保存用户访问记录
        UserVisitRecord visitRecord = new UserVisitRecord();
        visitRecord.setDate(DateUtil.date().toString("yyyy-MM-dd"));
        visitRecord.setUid(userService.getUserId());
        visitRecord.setVisitType(1);
        userVisitRecordService.save(visitRecord);
        return indexInfoResponse;
    }

    /**
     * 热门搜索
     *
     * @return List<HashMap < String, String>>
     */
    @Override
    public List<HashMap<String, Object>> hotKeywords() {
        return systemGroupDataService.getListMapByGid(SysGroupDataConstants.GROUP_DATA_ID_INDEX_KEYWORDS);
    }

    /**
     * 微信分享配置
     *
     * @return Object
     */
    @Override
    public HashMap<String, String> getShareConfig() {
        HashMap<String, String> map = new HashMap<>();
        HashMap<String, String> info = systemConfigService.info(Constants.CONFIG_FORM_ID_PUBLIC);
        if (info == null) {
            throw new CrmebException("请配置公众号分享信息！");
        }
        map.put("img", info.get(SysConfigConstants.CONFIG_KEY_ADMIN_WECHAT_SHARE_IMAGE));
        map.put("title", info.get(SysConfigConstants.CONFIG_KEY_ADMIN_WECHAT_SHARE_TITLE));
        map.put("synopsis", info.get(SysConfigConstants.CONFIG_KEY_ADMIN_WECHAT_SHARE_SYNOSIS));
        return map;
    }

    /**
     * 获取会员充值相关的配置
     *
     * @return Map<String, String>
     */
    @Override
    public HashMap<String, String> getVipRechargeConfig() {
        HashMap<String, String> map = new HashMap<>();
        // 查询代理商邀请奖励配置（加agent后缀）
        HashMap<String, String> agentInviteConfig = systemConfigService.info(Constants.CONFIG_FORM_ID_INVITE_REWARD_FOR_AGENT);
        if (agentInviteConfig != null) {
            for (String key : agentInviteConfig.keySet()) {
                map.put(key + "_agent", agentInviteConfig.get(key));
            }
        }
        // 查询合作伙伴邀请奖励配置（加partner后缀）
        HashMap<String, String> partnerInviteConfig = systemConfigService.info(Constants.CONFIG_FORM_ID_INVITE_REWARD_FOR_PARTNER);
        if (partnerInviteConfig != null) {
            for (String key : partnerInviteConfig.keySet()) {
                map.put(key + "_partner", partnerInviteConfig.get(key));
            }
        }
        // 查询提现金额配置
        HashMap<String, String> withdrawConfig = systemConfigService.info(Constants.CONFIG_FORM_ID_VIP_WITHDRAW);
        if (withdrawConfig != null) {
            map.putAll(withdrawConfig);
        }
        // 查询代理和合作伙伴充值费配置
        HashMap<String, String> agentPartnerFeeConfig = systemConfigService.info(Constants.CONFIG_FORM_ID_AGENT_AND_PARTNER_FEE);
        if (agentPartnerFeeConfig != null) {
            map.putAll(agentPartnerFeeConfig);
        }
        return map;
    }

    /**
     * 获取首页商品列表
     *
     * @param type             类型 【1 精品推荐 2 热门榜单 3首发新品 4促销单品】
     * @param pageParamRequest 分页参数
     * @return List
     */
    @Override
    public CommonPage<IndexProductResponse> findIndexProductList(Integer type, PageParamRequest pageParamRequest) {
        if (type < Constants.INDEX_HOT_BANNER || type > Constants.INDEX_BEST_BANNER) {
            return CommonPage.restPage(new ArrayList<>());
        }
        List<StoreProduct> storeProductList = storeProductService.getIndexProduct(type, pageParamRequest);
        if (CollUtil.isEmpty(storeProductList)) {
            return CommonPage.restPage(new ArrayList<>());
        }
        CommonPage<StoreProduct> storeProductCommonPage = CommonPage.restPage(storeProductList);

        List<IndexProductResponse> productResponseArrayList = new ArrayList<>();
        for (StoreProduct storeProduct : storeProductList) {
            IndexProductResponse productResponse = getIndexProductResponse(storeProduct);
            productResponseArrayList.add(productResponse);
        }
        CommonPage<IndexProductResponse> productResponseCommonPage = CommonPage.restPage(productResponseArrayList);
        BeanUtils.copyProperties(storeProductCommonPage, productResponseCommonPage, "list");
        return productResponseCommonPage;
    }

    @NotNull
    private IndexProductResponse getIndexProductResponse(StoreProduct storeProduct) {
        IndexProductResponse productResponse = new IndexProductResponse();
        productResponse.setId(storeProduct.getId());

        // 获取当前用户信息
        User currentUser = userService.getInfo();

        // 获取用户等级佣金比例
        BigDecimal userCommissionRate = getUserCommissionRate(currentUser);

        // 先计算返现金额：商品价格 * 商品返现率 * 会员等级佣金比例
        if (storeProduct.getPrice() != null && storeProduct.getCashBackRate() != null &&
            storeProduct.getPrice().compareTo(BigDecimal.ZERO) > 0) {
            productResponse.setCashBackAmount(BigDecimal.ZERO);
            productResponse.setCashBackRate(BigDecimal.ZERO);
            // 如果商品返现率小于0，直接返回零
            if (storeProduct.getCashBackRate().compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal cashBackAmount = storeProduct.getPrice()
                        .multiply(storeProduct.getCashBackRate())
                        .multiply(userCommissionRate);
                productResponse.setCashBackAmount(cashBackAmount);

                // 再通过返现金额计算返现率：返现金额 / 商品价格
                BigDecimal cashBackRate = cashBackAmount.divide(storeProduct.getPrice(), 4, BigDecimal.ROUND_HALF_UP);
                productResponse.setCashBackRate(cashBackRate);
            }
        }

        productResponse.setChannel(ProductChannelEnum.TIKTOK.getCode());
        productResponse.setMainImageUrl(storeProduct.getImage());
        productResponse.setSalesPrice(storeProduct.getPrice());
        productResponse.setMaxSalesPrice(storeProduct.getMaxSalesPrice());
        productResponse.setMinSalesPrice(storeProduct.getMinSalesPrice());
        productResponse.setTitle(storeProduct.getStoreName());
        productResponse.setShopName(storeProduct.getShopName());
        return productResponse;
    }

    /**
     * 根据用户等级获取对应的佣金比例
     *
     * @param user 用户信息
     * @return 佣金比例（百分比形式，需要除以100）
     */
    private BigDecimal getUserCommissionRate(User user) {
        int userLevel = 0; // 默认为普通用户等级

        // 如果用户已登录且有等级，使用用户的等级
        if (user != null && user.getLevel() != null) {
            userLevel = user.getLevel();
        }

        // 从用户等级表中获取佣金比例（包括普通用户等级0）
        SystemUserLevel systemUserLevel = systemUserLevelService.getByGrade(userLevel);
        if (systemUserLevel != null && systemUserLevel.getCommissionRate() != null) {
            // 佣金比例存储为百分比，需要转换为小数
            return systemUserLevel.getCommissionRate().divide(new BigDecimal("100"), 4, BigDecimal.ROUND_HALF_UP);
        }

        // 如果没有找到对应等级的配置，返回0
        return BigDecimal.ZERO;
    }

    @Override
    public CommonPage<IndexProductResponse> findBrandProductList(String code, Integer orderBy, Boolean isAsc, PageParamRequest pageParamRequest) {
        //查询品牌详情
        StoreBrandResponse storeBrandResponse = brandService.getDetail(code);
        //品牌code不存在
        if (storeBrandResponse == null) {
            return CommonPage.restPage(new ArrayList<>());
        }
        //查询商品信息
        List<StoreProduct> storeProductList = storeProductService.getBrandProduct(code, orderBy, isAsc, pageParamRequest);
        if (CollUtil.isEmpty(storeProductList)) {
            return CommonPage.restPage(new ArrayList<>());
        }
        CommonPage<StoreProduct> storeProductCommonPage = CommonPage.restPage(storeProductList);

        List<IndexProductResponse> productResponseArrayList = new ArrayList<>();
        for (StoreProduct storeProduct : storeProductList) {
            IndexProductResponse productResponse = getIndexProductResponse(storeProduct);
            productResponseArrayList.add(productResponse);
        }
        CommonPage<IndexProductResponse> productResponseCommonPage = CommonPage.restPage(productResponseArrayList);
        BeanUtils.copyProperties(storeProductCommonPage, productResponseCommonPage, "list");
        return productResponseCommonPage;
    }

    /**
     * 获取颜色配置
     *
     * @return SystemConfig
     */
    @Override
    public SystemConfig getColorConfig() {
        return systemConfigService.getColorConfig();
    }

    /**
     * 获取版本信息
     *
     * @return MyRecord
     */
    @Override
    public MyRecord getVersion(String platform) {
        if (platform == null || (!platform.equals("android") && !platform.equals("ios"))) {
            throw new CrmebException("不支持的平台类型:" + platform);
        }
        MyRecord record = new MyRecord();
        String version;
        String isDebug;
        if (platform.equals("android")) {
            version = systemConfigService.getValueByKey(Constants.CONFIG_APP_ANDROID_VERSION);
            isDebug = systemConfigService.getValueByKey(Constants.CONFIG_APP_ANDROID_IS_DEBUG);
        } else {
            version = systemConfigService.getValueByKey(Constants.CONFIG_APP_IOS_VERSION);
            isDebug = systemConfigService.getValueByKey(Constants.CONFIG_APP_IOS_IS_DEBUG);
        }
        String enableAppPay = systemConfigService.getValueByKey(Constants.CONFIG_ENABLE_APP_PAY);
        String appLoginMode = systemConfigService.getValueByKey(Constants.CONFIG_APP_LOGIN_MODE);
        // app版本号
        record.set("appVersion", version);
        record.set("debug", isDebug);
        record.set("enableAppPay", enableAppPay);
        record.set("appLoginMode", appLoginMode);
//        record.set("androidAddress", systemConfigService.getValueByKey(Constants.CONFIG_APP_ANDROID_ADDRESS));
//        record.set("iosAddress", systemConfigService.getValueByKey(Constants.CONFIG_APP_IOS_ADDRESS));
//        record.set("openUpgrade", systemConfigService.getValueByKey(Constants.CONFIG_APP_OPEN_UPGRADE));
        return record;
    }

    /**
     * 获取全局本地图片域名
     *
     * @return String
     */
    @Override
    public String getImageDomain() {
        String localUploadUrl = systemConfigService.getValueByKey("localUploadUrl");
        return StrUtil.isBlank(localUploadUrl) ? "" : localUploadUrl;
    }
}

