package com.genco.service.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.genco.common.model.user.UserBill;
import com.genco.common.request.RewardStatisticsRequest;
import com.genco.common.response.RewardStatisticsResponse;
import com.genco.common.response.UserBillResponse;

import java.util.List;
import java.util.Map;

/**
 * 用户账单表 Mapper 接口
 */
public interface UserBillDao extends BaseMapper<UserBill> {

    List<UserBillResponse> fundMonitoring(Map<String, Object> map);

    /**
     * 查询奖励发放明细
     *
     * @param request 查询请求参数
     * @return List<RewardStatisticsResponse.RewardDetailItem>
     */
    List<RewardStatisticsResponse.RewardDetailItem> selectRewardDetails(RewardStatisticsRequest request);
}
